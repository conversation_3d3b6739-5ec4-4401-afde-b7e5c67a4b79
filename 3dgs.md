### 摘要
随着计算机图形学与计算机视觉的快速发展，三维重建技术在多个领域展现出广阔的应用前景。传统显式几何表达方法难以处理复杂形状和噪声数据，而基于辐射场的隐式表达方法虽能生成高质量图像，但训练时间长且实时性较差。3D Gaussian Splatting (3DGS) 结合了传统方法和神经渲染技术的优势，以高斯函数为基础，采用可微渲染实现高质量、实时的三维场景表达与重建。本文系统综述了3DGS的基本原理、算法流程及其在速度、效率和渲染质量方面的改进方法，并探讨了其在游戏开发、虚拟现实、即时定位与地图构建（SLAM）等领域的应用场景。最后，总结了3DGS的优缺点与未来发展趋势，为三维重建技术的研究与实践提供参考。

### 引言
随着计算机图形学和计算机视觉技术的快速发展，三维重建技术在游戏开发、虚拟现实、自动驾驶等领域得到了广泛的应用。传统的被动式三维重建技术将多视角图像重建成点云、网格和体素等显式离散的表达方法。这些常用的三维物体表达方法适合于GPU进行快速地光栅化渲染，但是由于传统三维重建算法的限制，其在处理复杂和可变形的三维形状方面效率低下，难以准确捕捉复杂的细节，也难以处理不完整或有噪声的数据。{1} 且离散的三维物体表达方法使其很难使用可微渲染的方法对场景进行优化。

近年来，使用辐射场（Radiance Fields）作为三维物体表达方式的三维重建方法迅速地发展。 以NeRF（Neural Radiance Fields）为代表的神经渲染将经典计算机图形学与机器学习相结合，从现实世界的观察中合成图像。NeRF利用神经网络隐式地表达三维场景，它能够为相关三维空间内的每个点创建颜色和密度，并通过体渲染技术生成高质量的新视角图像。NeRF方法虽然能够实现高质量的三维重建，但其训练时间长、渲染速度慢，难以满足实时应用的需求。3DGS（3D Gaussian Splatting）结合了传统方法和 NeRF 方法的优点，能够以较低的计算成本实现高质量、实时的三维场景渲染。3DGS采用多个高斯函数表达三维物体，每个高斯函数包含位置、颜色、形状和透明度等参数，并通过可微渲染技术对高斯参数进行优化，并通过泼溅（Splatting）的方式进行实时渲染。

本文将对三维物体的场表达与3DGS技术进行全面的综述，包括其基本原理、改进方法、应用场景、优缺点以及未来发展趋势。#TODO

### 背景
#### 三维物体的表达
##### 传统的三维物体表达方法
传统的3D表达方法主要依赖于显式几何表达，常见的有以下几种：    

点云： 通过记录空间中点的三维坐标来表达物体或场景的表面。这种方法简单直接，但难以表达物体的细节和拓扑结构。
网格： 使用顶点和面片来描述物体的表面，能够更精确地表达物体的几何形状，但对拓扑结构变化敏感，难以处理复杂的变形。
体素： 将三维空间划分为规则的立方体单元，每个单元存储该区域的信息，例如颜色、密度等。这种方法可以表达复杂的内部结构，但存储效率较低。

##### 三维物体的场表达
辐射场（Radiance Fields）是一种通过记录3D空间中光线分布来表达三维物体或场景的方法，它捕捉光如何与环境中的表面和材质相互作用 [Neural fields in visual computing and beyond]。辐射场可被形式化的描述为函数 $L:\mathbf(R)^5 \rightarrow \mathbf(R)^+$ ，其中 $L(x,y,z,\theta,\phi)$ 将空间中的点 $(x,y,z)$ 和球面坐标 $(\theta,\phi)$ 指定的方向映射到非负辐射值。辐射场可以通过隐式或显式表达来封装，每种表达方式在场景表达和渲染方面都有特定的优势。

NeRF使用的神经辐射场（Neural Radiance Fields）是一种隐式的辐射场表达。神经辐射场通过使用神经网络隐式地存储辐射场，来表达场景中的光分布，而不明确定义场景中的几何体。具体的，神经辐射场通过使用多层感知机（MLP）将空间坐标 $(x，y，z)$ 和观察方向 $(\theta,\phi)$ 映射到颜色和密度值，即 $L:(x,y,z,\theta,\phi) \rightarrow (R,G,B,\sigma)$，其中 $L(x,y,z,\theta,\phi) = MLP(x,y,z,\theta,\phi)$。任何点的辐射度都不会显式存储，而是通过查询 MLP 动态计算。

3DGS则使用高斯函数来在三维空间中显式地表达辐射场，通过将表达不同方向颜色的球谐函数与透明度信息附加在每个高斯函数上来表达场景中的光分布。这些高斯函数的参数可以根据不同视角图像的信息通过神经网络进行优化，以准确表达场景。具体的，辐射场中每个每个位置的光分布可以用所有高斯函数在该位置的影响来计算，即 $L(x,y,z,\theta,\phi) = \sum_i G(x,y,z,\mu_i,\Sigma_i) \cdot c_i(\theta, \phi)$，其中 $G$ 为均值为 $\mu_i$ 协方差矩阵为 $\Sigma_i$ 的高斯函数， $c$ 为表达不同方向颜色的球谐函数。

### 3DGS 的原理和算法流程
3DGS根据输入的不同视角的图像使用数百万个3D高斯函数来对场景进行重建，以辐射场的方式表达三维场景，并利用泼溅（Splatting）的方式高效的进行渲染。以下是详细的数学原理描述：

#### 1. 3D高斯表示

场景被表示为一组3D高斯函数，每个高斯函数都可以被看作是一个模糊的、有方向性的椭球体，用于描述空间中的一个区域的颜色和透明度。一个3D高斯函数 $G(x)$ 在空间中的点 $x$ 处的值由以下公式定义：

\[
    G(\bm{x}) = α \cdot exp(-(1/2)  (\bm{x} - \bm{μ})ᵀ  \bm{Σ}  (\bm{x} - \bm{μ}))
\]

其中：$x$ 表示3D空间中的一个点的位置 $(x, y, z)$。$\bm{μ}$ 表示高斯函数均值即的中心点 $(μx, μy, μz)$，也就是高斯在空间中的位置。$\bm{Σ}$ 为高斯函数的协方差矩阵，描述了高斯在各个方向上的扩展程度和形状。$α$ 为高斯的不透明度 (opacity)，控制该高斯对最终渲染颜色的贡献程度。

由于协方差矩阵仅为半正定矩阵时才有物理意义，直接使用随机梯度下降法优化协防差矩阵容易得到无效的结果，为了更好地控制高斯的形状和方向来对其进行优化，3DGS将协方差矩阵 Σ 分解为一个旋转矩阵 **R** 和一个缩放矩阵 **S**：

\[
    Σ = R  S  Sᵀ  Rᵀ
\]

其中 $S$ 为缩放矩阵，其对角线元素 $s = (sx, sy, sz)$ 分别表示高斯在三个主轴方向上的缩放因子。而 $R$ 为旋转矩阵，表示高斯的朝向，由四个四元数参数 $q = (qw, qx, qy, qz)$ 表示。

为了储存辐射场的光分布信息，每个高斯函数除了位置、形状和不透明度之外，还关联了一个颜色信息 $c$，通常表示为RGB颜色向量。该颜色信息通常使用球谐函数 (Spherical Harmonics, SH) 表示，以支持视图相关的效果。

#### 2. 高斯泼溅渲染


给定一个相机视角，渲染该视角的图像需要将3D高斯投影到2D图像平面上，然后对投影到2D图像平面上的高斯函数进行混合得到最终渲染结果。

视图变换 (View Transformation) 使用视图变换矩阵 **W** 将高斯中心点 μ 从世界坐标系变换到相机坐标系：

\[
        μ' = W * μ
\]

同样地，协方差矩阵也需要变换到相机坐标系：

\[
        Σ' = J  W  Σ  Wᵀ  Jᵀ
\]

其中，**J** 是投影变换的雅可比矩阵。

投影后的高斯仍然是一个高斯函数，但现在是在2D图像平面上。它的中心点为投影后的 μ'，协方差矩阵为 Σ' 的前两行两列。

接下来需要对投影到2D图像平面上的高斯函数进行透明度混合 (Alpha Blending)。在图像平面上的每个像素，需要计算所有覆盖该像素的2D高斯贡献的颜色。3DGS 使用基于tile的光栅化器进行高效的透明度混合。对于每个像素，它会按照从前到后的顺序对高斯进行排序，并使用以下公式计算最终颜色：

\[
    C = Σᵢ cᵢ  αᵢ  ∏_{j<i} (1 - αⱼ)
\]

其中： C 为最终的像素颜色。cᵢ 为第 i 个高斯的颜色。αᵢ 为第 i 个高斯的投影后的2D不透明度，可以从投影后的2D高斯函数计算得出。 $∏_{j<i} (1 - αⱼ)$ 表示在第 i 个高斯之前的所有高斯的不透明度的累乘，用于计算当前高斯函数的被遮挡程度。

#### 3. 3DGS的优化

3DGS 使用L1 损失和结构相似性指数 (SSIM) 的组合作为损失函数来衡量渲染图像和真实图像之间的差异：

$\mathcal{L} = (1-\lambda)\mathcal{L}_1 + \lambda\mathcal{L}_{D-SSIM} $ 

通过反向传播算法，可以计算损失函数相对于每个高斯参数$(μ, q, s, α, c)$的梯度。其中 $q$ 与 $s$ 为高斯函数的协方差矩阵分解得到的高斯球的旋转与缩放，对这两个参数进行优化以防得到无效的协方差矩阵。由于整个渲染过程是可微分的，因此可以使用随机梯度下降等优化算法来更新这些参数，使渲染图像更接近真实图像。


在优化过程中，3DGS 使用自适应密度控制 (Adaptive Density Control)的方法根据需要对高斯进行密集化和精简。如对于欠重建的区域（即渲染图像和真实图像之间差异较大的区域），会复制或分裂高斯，以增加该区域的表示能力。而对于过度重建或透明度非常低的区域，会移除或合并高斯，以减少冗余并提高效率。

#### 3DGS的算法流程

3DGS的算法流程可以总结如下：

输入. 多个视角的图像组以及需要渲染的任意视角.
输出. 给定视角的渲染图.
Step1. 使用 SfM 算法从一组图像中估计相机位姿和稀疏点云.
Step2. 将 SfM 生成的稀疏点云中的每个点转换为一个三维高斯函数，初始化其参数.
Step3. 使用泼溅渲染得到某个输入图像视角的渲染图像.
Step4. 将渲染图像与真实图像比较，根据损失函数使用反向传播更新高斯函数的参数.
Step5. 使用自适应密度控制方法对高斯球进行增密或精简.
Step6. 重复 Step3-Step5 直到高斯函数的参数收敛
Step7. 根据给定的视角渲染得到图像.

### 3DGS 的优缺点
#### 优点
高质量的渲染效果： 3DGS 能够捕捉精细的细节和复杂的灯光效果，例如反射和折射，从而生成高度逼真的图像 。  

高效的渲染速度： 相较NeRF使用体渲染的方式需要对每条光线进行多次采样和神经网络查询，3DGS通过泼溅将高斯函数投影到二维空间，能够实现实时或接近实时的渲染速度，适用于交互式应用程序 。 

紧凑的数据表示：与密集的多边形网格或数据量大的神经网络相比，高斯函数表示更加紧凑，这意味着需要更少的存储空间和计算能力。  

可编辑性：由于3DGS的高斯函数是三维物体场表达的显式表达方式，可以很容易地对高斯函数的形态、位置、颜色等信息进行编辑从而对场景进行修改，相较NeRF使用隐式的神经网络表达在可编辑性上更优。

鲁棒性： 3DGS 对噪声具有更好的鲁棒性，产生的视觉伪影更少，并且能够比过去的技术更可靠地处理三维重建中传统上具有挑战性的方面，例如透明度、反照率等。   

#### 缺点
高斯函数数量过多： 使用数百万个高斯函数来表示场景会导致较高的内存占用，例如，典型场景的内存占用为 700Mb-1.2Gb。 

需要从多个角度捕捉场景： 为了获得高质量的重建结果，需要从多个角度拍摄场景图像。   
无法直接生成网格： 原始的 3DGS 实现无法直接生成网格模型 。

对噪声敏感： 在少样本情况下，3DGS 比基于神经辐射场的三维重建更容易受到噪声的影响 。   


### 3DGS 的改进方法

为了进一步提高 3DGS 的性能，研究人员提出了各种改进方法，主要集中在以下几个方面：

#### 速度和效率方面的改进
{reduced_3DGS_i3d}观察到在3DGS渲染给定分辨率的图像时，可能出现密集的高斯函数簇投影到同一像素，这些高斯函数所包含的细节数量超出了从任何视图中所能分辨的范围。据此该文章提出了基于分辨率感知的高斯函数剪枝策略。该策略通过评估空间中的冗余度分数（Redundancy Score），识别并去除不必要的高斯函数。同时，该文章使用自适应的球谐函数阶数，通过评估每个高斯的球谐函数，确定其是否需要高阶球谐函数来表示颜色。对于许多漫反射材质，仅使用 RGB 颜色值即可，无需存储额外的球谐函数。进一步的，该文章对高斯函数的不透明度、缩放、旋转和球谐函数系数等属性进行量化，压缩其内存占用。

3DGS-LM 方法使用 Levenberg-Marquardt (LM) 优化器代替 ADAM 优化器，以加速 3DGS 的重建过程。3DGS-LM 方法将3DGS渲染的损失函数修改为平方和能量函数，并使用 LM 算法替代 ADAM 优化器进行优化。为了使 LM 算法的计算效率更高，该文章还使用了一种高效的 GPU 并行化方案，用于 LM 算法内部的预处理共轭梯度（PCG）算法，，并定制了 CUDA 内核来计算雅可比向量积。

FCGS 提出了一种无需优化的压缩方法，可以在一次前馈传递中快速压缩 3DGS 场景，以达到快速对3DGS进行压缩的目的。由于 3DGS 的高斯属性对偏差非常敏感，直接将所有属性馈送到自动编码器会导致保真度显著下降。为解决此问题，FCGS 引入了多路径熵模块（MEM）来自适应地确定高斯属性是应通过自动编码器进行压缩，还是直接量化。为了进一步提高概率估计的准确性，FCGS 采用了上下文模型。其核心思想是利用已解码的部分数据来预测剩余部分，通过利用 3D 和 2D 网格结构来捕捉高斯属性之间的空间关系，从而消除高斯之间的冗余。通过结合 MEM 和上下文模型，FCGS 实现了超过 20 倍压缩，同时保持了高保真度。



#### 渲染质量与细节方面的改进
由于3D高斯的噪声和离散性质阻碍了对物体表面的准确估计，{Normal-GS}将表面法线融入 3DGS 管道，以提高渲染质量和几何精度。Normal-GS 使用基于物理的渲染方程来模拟法线和入射光之间的相互作用，将表面颜色重新参数化为法线与设计的积分方向照明向量 (IDIV) 的乘积。IDIV 捕获局部入射光，无需依赖全局环境图，从而提供更灵活和准确的框架来优化表面法线。Normal-GS 还利用优化的法线和积分方向编码 (IDE) 来精确建模镜面反射效果，从而增强渲染质量和表面法线精度。

{AtomGs}等人观察到3DGS的将优化参数与自适应密度控制相结合的策略可能会导致噪点与伪影的产生。AtomGS先使用各向同性的原子高斯球进行分布，并对这些高斯函数的分布进行优化，相较各向异性的高斯函数，原子高斯可以在快速地优化后更精确地分布在物体表面。接下来对高斯球进行细分与合并的操作，合并表示大而光滑表面的高斯函数，同时保留表示细节的高斯函数。为了解决高斯函数不总是代表实际几何结构的问题，AtomGS 还利用了几何引导优化。其计算曲率贴图与边缘贴图，通过一个权重函数结合边缘贴图来对曲率贴图进行平滑操作。此权重函数的设计使得梯度较低的区域获得较高的权重，从而促进更多平滑，而梯度较高的区域获得较低的权重以保留细节。

{Mip-Splatting}针对 3DGS 在采样率变化时出现伪影的问题，提出了 Mip-Splatting 方法，该方法主要引入了两个新的滤波器：3D 平滑滤波器和 2D Mip 滤波器。3D 平滑滤波器的作用是限制 3D 高斯基元的最大频率，使其与训练图像的采样约束相匹配。其根据 Nyquist-Shannon 采样定理，3D 场景的最高频率受训练视图定义的采样率的限制。2D Mip 滤波器的作用是模拟物理成像过程中的盒式滤波器，以实现任意尺度下的无锯齿渲染。

{Deblur} 提出了一种名为 Deblurring 3DGS 的新型实时去模糊框架，旨在改进3DGS，使其能够从模糊图像中重建精细和清晰的细节。其对高斯函数表达中的散焦模糊与相机运动模糊两种模糊进行建模，使用多层感知机从清晰图片优化的3DGS中生成模拟模糊的3DGS。然后将其作为输入使用神经网络训练达到去模糊的目的。Deblurring 3DGS 即使从模糊图像中也可以重建精细和清晰的细节。



### 3DGS 的应用场景

3DGS 凭借其高质量、实时渲染的特性，在各个领域都有着广泛的应用前景，例如：

三维语义分割： 由于三维数据量相较二维更多，三维场景的物体分割一直是一个很有挑战性的任务，传统的三维场景的分割方法通常基于2D投影切片的反投影、体素或点云等方法。由于3DGS中高斯球与点云类似，可以利用3DGS携带的额外信息改进点云分割的方法。PointNet++ 最初是为点云分割而开发的，现在可以作为 3DGS 分割模型 。{Semantic 3D segmentation of 3D Gaussian Splats}

创建真实感虚拟人物： Meta{Relightable Gaussian Codec Avatars} 对虚拟人物的3DGS实验展示了其在 VR 环境中实现真实感与远程实时渲染的能力。其使用可编辑的3DGS场景实现了虚拟人物的表情变换以及实时的光照变化。

即时定位与地图重建（SLAM）：SplaTAM使用3DGS在现实场景中进行精确的相机跟踪和高保真重建，展示了其在复杂空间映射和 3D 重建中的实用性。

游戏开发： 用于创建逼真的游戏场景和角色模型，并实现实时的游戏渲染。例如，游戏引擎 Unreal Engine 通过使用插件可以将 3DGS 技术集成到引擎中，为游戏开发者提供更强大的工具 。{(https://www.fab.com/listings/f1b149e9-7f55-4bad-87ff-29ae227942b5)}

医疗影像： 用于重建医学数据的三维模型，并进行疾病诊断和治疗规划。例如，3DGS 可以用于创建人体器官的三维模型，帮助医生进行手术规划。 {随便找一个}

自动驾驶： 传统方法从具有少量视角的车载传感器数据快速重建出道路的三维场景是十分困难的，特别是高速运动下，用于重建的图像很可能有一定的模糊。{DrivingGaussian} 利用3DGS的能力在有限视角下重建出更精确的场景


### 8. 结论
3DGS 作为一种新兴的三维重建技术，其利用三维物体场表达与可微渲染，具有高质量、实时渲染、紧凑数据表示等优点，在三维语义分割、即时定位与地图重建、游戏开发等领域具有广阔的应用前景。与 NeRF 相比，3DGS 在渲染速度、对噪声的鲁棒性以及可编辑性等方面具有优势，但其存储空间占用较大。随着研究的深入和技术的不断发展，3DGS 将在未来发挥更大的作用，并推动三维重建技术的发展。