This is XeTeX, Version 3.141592653-2.6-0.999996 (TeX Live 2024) (preloaded format=xelatex 2024.4.17)  13 JUL 2025 15:23
entering extended mode
 restricted \write18 enabled.
 file:line:error style messages enabled.
 %&-line parsing enabled.
**bare_jrnl_new_sample4
(./bare_jrnl_new_sample4.tex
LaTeX2e <2023-11-01> patch level 1
L3 programming layer <2024-02-20>
(./IEEEtran.cls
Document Class: IEEEtran 2015/08/26 V1.8b by <PERSON>
-- See the "IEEEtran_HOWTO" manual for usage information.
-- http://www.michaelshell.org/tex/ieeetran/
\@IEEEtrantmpdimenA=\dimen140
\@IEEEtrantmpdimenB=\dimen141
\@IEEEtrantmpdimenC=\dimen142
\@IEEEtrantmpcountA=\count184
\@IEEEtrantmpcountB=\count185
\@IEEEtrantmpcountC=\count186
\@IEEEtrantmptoksA=\toks17
LaTeX Font Info:    Trying to load font information for TU+ptm on input line 503.
LaTeX Font Info:    No file TUptm.fd. on input line 503.

LaTeX Font Warning: Font shape `TU/ptm/m/n' undefined
(Font)              using `TU/lmr/m/n' instead on input line 503.

-- Using 8.5in x 11in (letter) paper.
-- Using DVI output.
\@IEEEnormalsizeunitybaselineskip=\dimen143
-- This is a 10 point document.
\CLASSINFOnormalsizebaselineskip=\dimen144
\CLASSINFOnormalsizeunitybaselineskip=\dimen145
\IEEEnormaljot=\dimen146

LaTeX Font Warning: Font shape `TU/ptm/bx/n' undefined
(Font)              using `TU/ptm/m/n' instead on input line 1086.


LaTeX Font Warning: Font shape `TU/ptm/m/it' undefined
(Font)              using `TU/ptm/m/n' instead on input line 1086.


LaTeX Font Warning: Font shape `TU/ptm/bx/it' undefined
(Font)              using `TU/ptm/bx/n' instead on input line 1086.

\IEEEquantizedlength=\dimen147
\IEEEquantizedlengthdiff=\dimen148
\IEEEquantizedtextheightdiff=\dimen149
\IEEEilabelindentA=\dimen150
\IEEEilabelindentB=\dimen151
\IEEEilabelindent=\dimen152
\IEEEelabelindent=\dimen153
\IEEEdlabelindent=\dimen154
\IEEElabelindent=\dimen155
\IEEEiednormlabelsep=\dimen156
\IEEEiedmathlabelsep=\dimen157
\IEEEiedtopsep=\skip48
\c@section=\count187
\c@subsection=\count188
\c@subsubsection=\count189
\c@paragraph=\count190
\c@IEEEsubequation=\count191
\abovecaptionskip=\skip49
\belowcaptionskip=\skip50
\c@figure=\count192
\c@table=\count193
\@IEEEeqnnumcols=\count194
\@IEEEeqncolcnt=\count195
\@IEEEsubeqnnumrollback=\count196
\@IEEEquantizeheightA=\dimen158
\@IEEEquantizeheightB=\dimen159
\@IEEEquantizeheightC=\dimen160
\@IEEEquantizeprevdepth=\dimen161
\@IEEEquantizemultiple=\count197
\@IEEEquantizeboxA=\box51
\@IEEEtmpitemindent=\dimen162
\IEEEPARstartletwidth=\dimen163
\c@IEEEbiography=\count198
\@IEEEtranrubishbin=\box52
) (f:/dev/texlive/2024/texmf-dist/tex/latex/amsmath/amsmath.sty
Package: amsmath 2023/05/13 v2.17o AMS math features
\@mathmargin=\skip51

For additional information on amsmath, use the `?' option.
(f:/dev/texlive/2024/texmf-dist/tex/latex/amsmath/amstext.sty
Package: amstext 2021/08/26 v2.01 AMS text
 (f:/dev/texlive/2024/texmf-dist/tex/latex/amsmath/amsgen.sty
File: amsgen.sty 1999/11/30 v2.0 generic functions
\@emptytoks=\toks18
\ex@=\dimen164
)) (f:/dev/texlive/2024/texmf-dist/tex/latex/amsmath/amsbsy.sty
Package: amsbsy 1999/11/29 v1.2d Bold Symbols
\pmbraise@=\dimen165
) (f:/dev/texlive/2024/texmf-dist/tex/latex/amsmath/amsopn.sty
Package: amsopn 2022/04/08 v2.04 operator names
)
\inf@bad=\count199
LaTeX Info: Redefining \frac on input line 234.
\uproot@=\count266
\leftroot@=\count267
LaTeX Info: Redefining \overline on input line 399.
LaTeX Info: Redefining \colon on input line 410.
\classnum@=\count268
\DOTSCASE@=\count269
LaTeX Info: Redefining \ldots on input line 496.
LaTeX Info: Redefining \dots on input line 499.
LaTeX Info: Redefining \cdots on input line 620.
\Mathstrutbox@=\box53
\strutbox@=\box54
LaTeX Info: Redefining \big on input line 722.
LaTeX Info: Redefining \Big on input line 723.
LaTeX Info: Redefining \bigg on input line 724.
LaTeX Info: Redefining \Bigg on input line 725.
\big@size=\dimen166
LaTeX Font Info:    Redeclaring font encoding OML on input line 743.
LaTeX Font Info:    Redeclaring font encoding OMS on input line 744.
\macc@depth=\count270
LaTeX Info: Redefining \bmod on input line 905.
LaTeX Info: Redefining \pmod on input line 910.
LaTeX Info: Redefining \smash on input line 940.
LaTeX Info: Redefining \relbar on input line 970.
LaTeX Info: Redefining \Relbar on input line 971.
\c@MaxMatrixCols=\count271
\dotsspace@=\muskip16
\c@parentequation=\count272
\dspbrk@lvl=\count273
\tag@help=\toks19
\row@=\count274
\column@=\count275
\maxfields@=\count276
\andhelp@=\toks20
\eqnshift@=\dimen167
\alignsep@=\dimen168
\tagshift@=\dimen169
\tagwidth@=\dimen170
\totwidth@=\dimen171
\lineht@=\dimen172
\@envbody=\toks21
\multlinegap=\skip52
\multlinetaggap=\skip53
\mathdisplay@stack=\toks22
LaTeX Info: Redefining \[ on input line 2953.
LaTeX Info: Redefining \] on input line 2954.
) (f:/dev/texlive/2024/texmf-dist/tex/latex/amsfonts/amsfonts.sty
Package: amsfonts 2013/01/14 v3.01 Basic AMSFonts support
\symAMSa=\mathgroup4
\symAMSb=\mathgroup5
LaTeX Font Info:    Redeclaring math symbol \hbar on input line 98.
LaTeX Font Info:    Overwriting math alphabet `\mathfrak' in version `bold'
(Font)                  U/euf/m/n --> U/euf/b/n on input line 106.
) (f:/dev/texlive/2024/texmf-dist/tex/latex/algorithms/algorithmic.sty
Invalid UTF-8 byte or sequence at line 11 replaced by U+FFFD.
Package: algorithmic 2009/08/24 v0.1 Document Style `algorithmic'
 (f:/dev/texlive/2024/texmf-dist/tex/latex/base/ifthen.sty
Package: ifthen 2022/04/13 v1.1d Standard LaTeX ifthen package (DPC)
) (f:/dev/texlive/2024/texmf-dist/tex/latex/graphics/keyval.sty
Package: keyval 2022/05/29 v1.15 key=value parser (DPC)
\KV@toks@=\toks23
)
\c@ALC@unique=\count277
\c@ALC@line=\count278
\c@ALC@rem=\count279
\c@ALC@depth=\count280
\ALC@tlm=\skip54
\algorithmicindent=\skip55
) (f:/dev/texlive/2024/texmf-dist/tex/latex/algorithms/algorithm.sty
Invalid UTF-8 byte or sequence at line 11 replaced by U+FFFD.
Package: algorithm 2009/08/24 v0.1 Document Style `algorithm' - floating environment
 (f:/dev/texlive/2024/texmf-dist/tex/latex/float/float.sty
Package: float 2001/11/08 v1.3d Float enhancements (AL)
\c@float@type=\count281
\float@exts=\toks24
\float@box=\box55
\@float@everytoks=\toks25
\@floatcapt=\box56
)
\@float@every@algorithm=\toks26
\c@algorithm=\count282
) (f:/dev/texlive/2024/texmf-dist/tex/latex/tools/array.sty
Package: array 2023/10/16 v2.5g Tabular extension package (FMi)
\col@sep=\dimen173
\ar@mcellbox=\box57
\extrarowheight=\dimen174
\NC@list=\toks27
\extratabsurround=\skip56
\backup@length=\skip57
\ar@cellbox=\box58
) (f:/dev/texlive/2024/texmf-dist/tex/latex/subfig/subfig.sty
Package: subfig 2005/06/28 ver: 1.3 subfig package
 (f:/dev/texlive/2024/texmf-dist/tex/latex/caption/caption3.sty
Package: caption3 2023/07/31 v2.4d caption3 kernel (AR)
\caption@tempdima=\dimen175
\captionmargin=\dimen176
\caption@leftmargin=\dimen177
\caption@rightmargin=\dimen178
\caption@width=\dimen179
\caption@indent=\dimen180
\caption@parindent=\dimen181
\caption@hangindent=\dimen182
Package caption Info: Unknown document class (or package),
(caption)             standard defaults will be used.
Package caption Info: \@makecaption = \long macro:#1#2->\ifx \@captype \@IEEEtablestring \footnotesize \bgroup \par \centering \@IEEEtabletopskipstrut {\normalfont \footnotesize #1}\\{\normalfont \footnotesize \scshape #2}\par \addvspace {0.5\baselineskip }\egroup \@IEEEtablecaptionsepspace \else \@IEEEfigurecaptionsepspace \setbox \@tempboxa \hbox {\normalfont \footnotesize {#1.}\nobreakspace \nobreakspace #2}\ifdim \wd \@tempboxa >\hsize \setbox \@tempboxa \hbox {\normalfont \footnotesize {#1.}\nobreakspace \nobreakspace }\parbox [t]{\hsize }{\normalfont \footnotesize \noindent \unhbox \@tempboxa #2}\else \ifCLASSOPTIONconference \hbox to\hsize {\normalfont \footnotesize \hfil \box \@tempboxa \hfil }\else \hbox to\hsize {\normalfont \footnotesize \box \@tempboxa \hfil }\fi \fi \fi  on input line 1175.
)
\c@KVtest=\count283
\sf@farskip=\skip58
\sf@captopadj=\dimen183
\sf@capskip=\skip59
\sf@nearskip=\skip60
\c@subfigure=\count284
\c@subfigure@save=\count285
\c@lofdepth=\count286
\c@subtable=\count287
\c@subtable@save=\count288
\c@lotdepth=\count289
\sf@top=\skip61
\sf@bottom=\skip62
) (f:/dev/texlive/2024/texmf-dist/tex/latex/base/textcomp.sty
Package: textcomp 2020/02/02 v2.0n Standard LaTeX package
) (f:/dev/texlive/2024/texmf-dist/tex/latex/sttools/stfloats.sty
Package: stfloats 2017/03/27 v3.3 Improve float mechanism and baselineskip settings
\@dblbotnum=\count290
\c@dblbotnumber=\count291
) (f:/dev/texlive/2024/texmf-dist/tex/latex/url/url.sty
\Urlmuskip=\muskip17
Package: url 2013/09/16  ver 3.4  Verb mode for urls, etc.
) (f:/dev/texlive/2024/texmf-dist/tex/latex/tools/verbatim.sty
Package: verbatim 2023-11-06 v1.5v LaTeX2e package for verbatim enhancements
\every@verbatim=\toks28
\verbatim@line=\toks29
\verbatim@in@stream=\read2
) (f:/dev/texlive/2024/texmf-dist/tex/latex/graphics/graphicx.sty
Package: graphicx 2021/09/16 v1.2d Enhanced LaTeX Graphics (DPC,SPQR)
 (f:/dev/texlive/2024/texmf-dist/tex/latex/graphics/graphics.sty
Package: graphics 2022/03/10 v1.4e Standard LaTeX Graphics (DPC,SPQR)
 (f:/dev/texlive/2024/texmf-dist/tex/latex/graphics/trig.sty
Package: trig 2021/08/11 v1.11 sin cos tan (DPC)
) (f:/dev/texlive/2024/texmf-dist/tex/latex/graphics-cfg/graphics.cfg
File: graphics.cfg 2016/06/04 v1.11 sample graphics configuration
)
Package graphics Info: Driver file: xetex.def on input line 107.
 (f:/dev/texlive/2024/texmf-dist/tex/latex/graphics-def/xetex.def
File: xetex.def 2022/09/22 v5.0n Graphics/color driver for xetex
))
\Gin@req@height=\dimen184
\Gin@req@width=\dimen185
) (f:/dev/texlive/2024/texmf-dist/tex/latex/cite/cite.sty
LaTeX Info: Redefining \cite on input line 302.
LaTeX Info: Redefining \nocite on input line 332.
Package: cite 2015/02/27  v 5.5
) (f:/dev/texlive/2024/texmf-dist/tex/latex/l3backend/l3backend-xetex.def
File: l3backend-xetex.def 2024-02-20 L3 backend support: XeTeX
\g__graphics_track_int=\count292
\l__pdf_internal_box=\box59
\g__pdf_backend_object_int=\count293
\g__pdf_backend_annotation_int=\count294
\g__pdf_backend_link_int=\count295
)

LaTeX Warning: Unused global option(s):
    [lettersize].

(./bare_jrnl_new_sample4.aux)
\openout1 = `bare_jrnl_new_sample4.aux'.

LaTeX Font Info:    Checking defaults for OML/cmm/m/it on input line 16.
LaTeX Font Info:    ... okay on input line 16.
LaTeX Font Info:    Checking defaults for OMS/cmsy/m/n on input line 16.
LaTeX Font Info:    ... okay on input line 16.
LaTeX Font Info:    Checking defaults for OT1/cmr/m/n on input line 16.
LaTeX Font Info:    ... okay on input line 16.
LaTeX Font Info:    Checking defaults for T1/cmr/m/n on input line 16.
LaTeX Font Info:    ... okay on input line 16.
LaTeX Font Info:    Checking defaults for TS1/cmr/m/n on input line 16.
LaTeX Font Info:    Trying to load font information for TS1+cmr on input line 16.
 (f:/dev/texlive/2024/texmf-dist/tex/latex/base/ts1cmr.fd
File: ts1cmr.fd 2023/04/13 v2.5m Standard LaTeX font definitions
)
LaTeX Font Info:    ... okay on input line 16.
LaTeX Font Info:    Checking defaults for TU/lmr/m/n on input line 16.
LaTeX Font Info:    ... okay on input line 16.
LaTeX Font Info:    Checking defaults for OMX/cmex/m/n on input line 16.
LaTeX Font Info:    ... okay on input line 16.
LaTeX Font Info:    Checking defaults for U/cmr/m/n on input line 16.
LaTeX Font Info:    ... okay on input line 16.


LaTeX Font Warning: Font shape `TU/ptm/b/n' undefined
(Font)              using `TU/ptm/bx/n' instead on input line 16.


LaTeX Font Warning: Font shape `TU/ptm/b/it' undefined
(Font)              using `TU/ptm/bx/it' instead on input line 16.


LaTeX Font Warning: Font shape `TU/ptm/b/n' undefined
(Font)              using `TU/ptm/bx/n' instead on input line 16.


LaTeX Font Warning: Font shape `TU/ptm/b/it' undefined
(Font)              using `TU/ptm/bx/it' instead on input line 16.


LaTeX Font Warning: Font shape `TU/ptm/b/n' undefined
(Font)              using `TU/ptm/bx/n' instead on input line 16.


LaTeX Font Warning: Font shape `TU/ptm/b/it' undefined
(Font)              using `TU/ptm/bx/it' instead on input line 16.


LaTeX Font Warning: Font shape `TU/ptm/b/n' undefined
(Font)              using `TU/ptm/bx/n' instead on input line 16.


LaTeX Font Warning: Font shape `TU/ptm/b/it' undefined
(Font)              using `TU/ptm/bx/it' instead on input line 16.


LaTeX Font Warning: Font shape `TU/ptm/b/n' undefined
(Font)              using `TU/ptm/bx/n' instead on input line 16.


LaTeX Font Warning: Font shape `TU/ptm/b/it' undefined
(Font)              using `TU/ptm/bx/it' instead on input line 16.


LaTeX Font Warning: Font shape `TU/ptm/b/n' undefined
(Font)              using `TU/ptm/bx/n' instead on input line 16.


LaTeX Font Warning: Font shape `TU/ptm/b/it' undefined
(Font)              using `TU/ptm/bx/it' instead on input line 16.


LaTeX Font Warning: Font shape `TU/ptm/b/n' undefined
(Font)              using `TU/ptm/bx/n' instead on input line 16.


LaTeX Font Warning: Font shape `TU/ptm/b/it' undefined
(Font)              using `TU/ptm/bx/it' instead on input line 16.


LaTeX Font Warning: Font shape `TU/ptm/b/n' undefined
(Font)              using `TU/ptm/bx/n' instead on input line 16.


LaTeX Font Warning: Font shape `TU/ptm/b/it' undefined
(Font)              using `TU/ptm/bx/it' instead on input line 16.


LaTeX Font Warning: Font shape `TU/ptm/b/n' undefined
(Font)              using `TU/ptm/bx/n' instead on input line 16.


LaTeX Font Warning: Font shape `TU/ptm/b/it' undefined
(Font)              using `TU/ptm/bx/it' instead on input line 16.


LaTeX Font Warning: Font shape `TU/ptm/b/n' undefined
(Font)              using `TU/ptm/bx/n' instead on input line 16.


LaTeX Font Warning: Font shape `TU/ptm/b/it' undefined
(Font)              using `TU/ptm/bx/it' instead on input line 16.

-- Lines per column: 58 (exact).
Package caption Info: Begin \AtBeginDocument code.
Package caption Info: subfig package v1.3 is loaded.
Package caption Info: End \AtBeginDocument code.

LaTeX Font Warning: Font shape `TU/ptm/b/n' undefined
(Font)              using `TU/ptm/bx/n' instead on input line 32.


LaTeX Font Warning: Font shape `TU/ptm/b/n' undefined
(Font)              using `TU/ptm/bx/n' instead on input line 36.


LaTeX Font Warning: Font shape `TU/ptm/m/sc' undefined
(Font)              using `TU/ptm/m/n' instead on input line 40.


LaTeX Font Warning: Font shape `TU/ptm/b/n' undefined
(Font)              using `TU/ptm/bx/n' instead on input line 41.


LaTeX Warning: Citation `smith2007statistical' on page 1 undefined on input line 41.


LaTeX Font Warning: Font shape `TU/ptm/b/n' undefined
(Font)              using `TU/ptm/bx/n' instead on input line 42.


LaTeX Warning: Citation `xie2022neural' on page 1 undefined on input line 42.


LaTeX Font Warning: Font shape `TU/ptm/b/n' undefined
(Font)              using `TU/ptm/bx/n' instead on input line 42.


LaTeX Warning: Citation `mildenhall2021nerf' on page 1 undefined on input line 42.


LaTeX Font Warning: Font shape `TU/ptm/b/n' undefined
(Font)              using `TU/ptm/bx/n' instead on input line 42.


LaTeX Warning: Citation `kerbl20233dgaussiansplattingrealtime' on page 1 undefined on input line 42.

LaTeX Font Info:    Trying to load font information for U+msa on input line 50.
(f:/dev/texlive/2024/texmf-dist/tex/latex/amsfonts/umsa.fd
File: umsa.fd 2013/01/14 v3.01 AMS symbols A
)
LaTeX Font Info:    Trying to load font information for U+msb on input line 50.
 (f:/dev/texlive/2024/texmf-dist/tex/latex/amsfonts/umsb.fd
File: umsb.fd 2013/01/14 v3.01 AMS symbols B
)

LaTeX Font Warning: Font shape `TU/ptm/b/n' undefined
(Font)              using `TU/ptm/bx/n' instead on input line 56.


LaTeX Warning: Citation `chen2025survey3dgaussiansplatting' on page 1 undefined on input line 56.


LaTeX Font Warning: Font shape `TU/ptm/b/n' undefined
(Font)              using `TU/ptm/bx/n' instead on input line 58.


LaTeX Warning: Citation `mildenhall2021nerf' on page 1 undefined on input line 58.

Missing character: There is no ， ("FF0C) in font cmr10!
Missing character: There is no ， ("FF0C) in font cmr10!
[1


]

LaTeX Font Warning: Font shape `TU/ptm/b/n' undefined
(Font)              using `TU/ptm/bx/n' instead on input line 66.


LaTeX Warning: Citation `kerbl20233dgaussiansplattingrealtime' on page 2 undefined on input line 66.


LaTeX Font Warning: Font shape `TU/ptm/b/n' undefined
(Font)              using `TU/ptm/bx/n' instead on input line 73.


LaTeX Warning: Citation `kerbl20233dgaussiansplattingrealtime' on page 2 undefined on input line 73.

Missing character: There is no α ("3B1) in font cmmi10!
Missing character: There is no μ ("3BC) in font cmbx10!
Missing character: There is no Σ ("3A3) in font cmbx10!
Missing character: There is no μ ("3BC) in font cmbx10!
Missing character: There is no μ ("3BC) in font cmmi10!
Missing character: There is no μ ("3BC) in font cmmi10!
Missing character: There is no μ ("3BC) in font cmmi10!

LaTeX Font Warning: Font shape `TU/ptm/b/n' undefined
(Font)              using `TU/ptm/bx/n' instead on input line 84.


LaTeX Warning: Citation `kerbl20233dgaussiansplattingrealtime' on page 2 undefined on input line 84.

Missing character: There is no Σ ("3A3) in font cmmi10!
Missing character: There is no Σ ("3A3) in font cmbx10!

LaTeX Font Warning: Font shape `TU/ptm/b/n' undefined
(Font)              using `TU/ptm/bx/n' instead on input line 114.


LaTeX Warning: Citation `kerbl20233dgaussiansplattingrealtime' on page 2 undefined on input line 114.

Missing character: There is no ∏ ("220F) in font cmr10!
Missing character: There is no α ("3B1) in font cmmi10!
[2]
Missing character: There is no μ ("3BC) in font cmmi10!
Missing character: There is no α ("3B1) in font cmmi10!

LaTeX Font Warning: Font shape `TU/ptm/b/n' undefined
(Font)              using `TU/ptm/bx/n' instead on input line 181.


LaTeX Warning: Citation `papantonakis2024reducing' on page 3 undefined on input line 181.

[3]

LaTeX Font Warning: Font shape `TU/ptm/b/n' undefined
(Font)              using `TU/ptm/bx/n' instead on input line 183.


LaTeX Warning: Citation `hollein20243dgs' on page 4 undefined on input line 183.


Underfull \hbox (badness 3514) in paragraph at lines 183--184
[]\TU/ptm/m/sc/10 The 3DGS-LM [?] method uses the Levenberg-
 []


LaTeX Font Warning: Font shape `TU/ptm/b/n' undefined
(Font)              using `TU/ptm/bx/n' instead on input line 185.


LaTeX Warning: Citation `chen2024fast' on page 4 undefined on input line 185.


LaTeX Font Warning: Font shape `TU/ptm/b/n' undefined
(Font)              using `TU/ptm/bx/n' instead on input line 188.


LaTeX Warning: Citation `wei2024normal' on page 4 undefined on input line 188.


LaTeX Font Warning: Font shape `TU/ptm/b/n' undefined
(Font)              using `TU/ptm/bx/n' instead on input line 190.


LaTeX Warning: Citation `liu2024atomgs' on page 4 undefined on input line 190.


LaTeX Font Warning: Font shape `TU/ptm/b/n' undefined
(Font)              using `TU/ptm/bx/n' instead on input line 192.


LaTeX Warning: Citation `yu2024mip' on page 4 undefined on input line 192.


LaTeX Font Warning: Font shape `TU/ptm/b/n' undefined
(Font)              using `TU/ptm/bx/n' instead on input line 194.


LaTeX Warning: Citation `lee2024deblurring' on page 4 undefined on input line 194.


LaTeX Font Warning: Font shape `TU/ptm/b/n' undefined
(Font)              using `TU/ptm/bx/n' instead on input line 203.


LaTeX Warning: Citation `jurski2024semantic' on page 4 undefined on input line 203.


LaTeX Font Warning: Font shape `TU/ptm/b/n' undefined
(Font)              using `TU/ptm/bx/n' instead on input line 204.


LaTeX Warning: Citation `saito2024relightablegaussiancodecavatars' on page 4 undefined on input line 204.


LaTeX Font Warning: Font shape `TU/ptm/b/n' undefined
(Font)              using `TU/ptm/bx/n' instead on input line 205.


LaTeX Warning: Citation `keetha2024splatam' on page 4 undefined on input line 205.


LaTeX Font Warning: Font shape `TU/ptm/b/n' undefined
(Font)              using `TU/ptm/bx/n' instead on input line 206.


LaTeX Warning: Citation `unrealengine' on page 4 undefined on input line 206.


LaTeX Font Warning: Font shape `TU/ptm/b/n' undefined
(Font)              using `TU/ptm/bx/n' instead on input line 207.


LaTeX Warning: Citation `cai2024radiative' on page 4 undefined on input line 207.


LaTeX Font Warning: Font shape `TU/ptm/b/n' undefined
(Font)              using `TU/ptm/bx/n' instead on input line 208.


LaTeX Warning: Citation `zhou2024drivinggaussian' on page 4 undefined on input line 208.

[4]
No file bare_jrnl_new_sample4.bbl.
[5

] (./bare_jrnl_new_sample4.aux)
 ***********
LaTeX2e <2023-11-01> patch level 1
L3 programming layer <2024-02-20>
 ***********


LaTeX Font Warning: Some font shapes were not available, defaults substituted.


LaTeX Warning: There were undefined references.

 ) 
Here is how much of TeX's memory you used:
 4204 strings out of 474773
 74496 string characters out of 5759509
 1951842 words of memory out of 5000000
 26319 multiletter control sequences out of 15000+600000
 562589 words of font info for 61 fonts, out of 8000000 for 9000
 1352 hyphenation exceptions out of 8191
 57i,10n,65p,1334b,205s stack positions out of 10000i,1000n,20000p,200000b,200000s

Output written on bare_jrnl_new_sample4.pdf (5 pages).
