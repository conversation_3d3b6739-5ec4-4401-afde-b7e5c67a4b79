\documentclass[lettersize,journal]{IEEEtran}
\usepackage{amsmath,amsfonts}
\usepackage{algorithmic}
\usepackage{algorithm}
\usepackage{array}
\usepackage[caption=false,font=normalsize,labelfont=sf,textfont=sf]{subfig}
\usepackage{textcomp}
\usepackage{stfloats}
\usepackage{url}
\usepackage{verbatim}
\usepackage{graphicx}
\usepackage{cite}
\hyphenation{op-tical net-works semi-conduc-tor IEEE-Xplore}
% updated with editorial comments 8/9/2021

\begin{document}

\title{3D Gaussian Splatting: A Survey}

\author{<PERSON><PERSON>, Huang}

% The paper headers
\markboth{Journal of \LaTeX\ Class Files,~Vol.~14, No.~8, August~2021}%
{Shell \MakeLowercase{\textit{et al.}}: A Sample Article Using IEEEtran.cls for IEEE Journals}

\IEEEpubid{0000--0000/00\$00.00~\copyright~2021 IEEE}
% Remember, if you use this you must call \IEEEpubidadjcol in the second
% column for its text to clear the IEEEpubid mark.

\maketitle

\begin{abstract}
With the rapid development of computer graphics and computer vision, 3D reconstruction technology has shown broad application prospects in many fields. Traditional explicit geometric expression methods are difficult to handle complex shapes and noisy data, and although the implicit expression method based on radiation field can generate high-quality images, it takes a long time to train and has poor real-time performance. 3D Gaussian Splatting (3DGS) combines the advantages of traditional methods and neural rendering technology. It is based on Gaussian functions and uses differentiable rendering to achieve high-quality, real-time 3D scene expression and reconstruction. This paper systematically reviews the basic principles, algorithm flow, and improvement methods of 3DGS in terms of speed, efficiency, and rendering quality, and explores its application scenarios in game development, virtual reality, and simultaneous localization and mapping (SLAM). Finally, the advantages and disadvantages of 3DGS and its future development trends are summarized to provide a reference for the research and practice of 3D reconstruction technology.
\end{abstract}

\begin{IEEEkeywords}
3D Gaussian Splatting, Radiance Field, Real-time Rendering.
\end{IEEEkeywords}

\section{Introduction}
With the rapid development of computer graphics and computer vision technology, 3D reconstruction technology has been widely used in game development, virtual reality, autonomous driving and other fields. Traditional passive 3D reconstruction technology reconstructs multi-view images into explicit discrete expressions such as point clouds, meshes and voxels\cite{smith2007statistical}. These commonly used 3D object expression methods are suitable for GPUs to perform fast rasterization rendering, but due to the limitations of traditional 3D reconstruction algorithms, they are inefficient in processing complex and deformable 3D shapes, difficult to accurately capture complex details, and difficult to process incomplete or noisy data. In addition, the discrete 3D object expression method makes it difficult to optimize the scene using differentiable rendering methods.
In recent years, 3D reconstruction methods using radiance fields \cite{xie2022neural} as a 3D object expression method have developed rapidly. Neural rendering, represented by NeRF (Neural Radiance Fields) \cite{mildenhall2021nerf}, combines classical computer graphics with machine learning to synthesize images from real-world observations. NeRF uses neural networks to implicitly express 3D scenes. It can create color and density for each point in the relevant 3D space and generate high-quality new view images through volume rendering technology. Although the NeRF method can achieve high-quality 3D reconstruction, its training time is long and the rendering speed is slow, which makes it difficult to meet the needs of real-time applications. 3DGS (3D Gaussian Splatting) \cite{kerbl20233dgaussiansplattingrealtime} combines the advantages of traditional methods and NeRF methods, and can achieve high-quality, real-time 3D scene rendering at a lower computational cost. 3DGS uses multiple Gaussian functions to express 3D objects. Each Gaussian function contains parameters such as position, color, shape, and transparency. The Gaussian parameters are optimized through differentiable rendering technology, and real-time rendering is performed through splatting.
This paper will comprehensively review the field expression of 3D objects and 3DGS technology, including its basic principles, advantages and disadvantages, improvement methods, and application scenarios.

\section{Background}
\subsection{Representation of 3D objects}
\subsubsection{Traditional methods of expressing 3D objects}
Traditional 3D expression methods mainly rely on explicit geometric expressions, the most common of which are as follows:
\begin{itemize}
    \item Point cloud: The surface of an object or scene is expressed by recording the 3D coordinates of a point in space. This method is simple and direct, but it is difficult to express the details and topological structure of the object.
    \item Mesh: Using vertices and patches to describe the surface of an object can more accurately express the geometric shape of the object, but it is sensitive to changes in topological structure and difficult to handle complex deformations.
    \item Voxel: Divide the 3D space into regular cubic units, each unit stores information about the area, such as color, density, etc. This method can express complex internal structures, but the storage efficiency is low.
\end{itemize}

\subsubsection{Field representation of 3D objects}
Radiance fields\cite{} are a method of representing three-dimensional objects or scenes by recording the distribution of light in 3D space, capturing how light interacts with surfaces and materials in the environment. Radiance fields can be formally described as a function $L:\mathbf(R)^5 \rightarrow \mathbf(R)^+$, where $L(x,y,z,\theta,\phi)$ maps a point in space $(x,y,z)$ and a direction specified by spherical coordinates $(\theta,\phi)$ to a non-negative radiance value\cite{chen2025survey3dgaussiansplatting}. Radiance fields can be encapsulated using either implicit or explicit representations, each of which has specific advantages in scene representation and rendering.

NeRF\cite{mildenhall2021nerf} uses Neural Radiance Fields (NRFs) as an implicit radiance field representation. NRFs expresses the light distribution in a scene by implicitly storing radiance fields using a neural network without explicitly defining the geometry in the scene. Specifically, NRFs uses a multi-layer perceptron (MLP) to map spatial coordinates $(x，y，z)$ and viewing directions $(\theta,\phi)$ to color and density values, i.e.:

$L:(x,y,z,\theta,\phi) \rightarrow (R,G,B,\sigma)$

The radiance of any point is not explicitly stored, but is dynamically calculated by querying the MLP:

$L(x,y,z,\theta,\phi) = MLP(x,y,z,\theta,\phi)$

3DGS\cite{kerbl20233dgaussiansplattingrealtime} uses Gaussian functions to explicitly express the radiation field in three-dimensional space. The light distribution in the scene is expressed by attaching spherical harmonic functions that express colors in different directions and transparency information to each Gaussian function. The parameters of these Gaussian functions can be optimized through neural networks based on the information of images from different perspectives to accurately express the scene. Specifically, the light distribution at each position in the radiation field can be calculated using the influence of all Gaussian functions at that position, namely:

$L(x,y,z,\theta,\phi) = \sum_i G(x,y,z,\mu_i,\Sigma_i) \cdot c_i(\theta, \phi)$

Where G is a Gaussian function with a mean of $\mu_i$ and a covariance matrix of $\Sigma_i$, and $c$ is a spherical harmonic function that expresses colors in different directions.

\section{3DGS principle and algorithm flow}
3DGS\cite{kerbl20233dgaussiansplattingrealtime} uses millions of 3D Gaussian functions to reconstruct the scene based on the input images from different perspectives, expresses the three-dimensional scene in the form of radiation field, and uses splatting to render efficiently. This chapter will discuss the mathematical principle description of 3DGS.

\subsection{Expression of gaussian function}
The scene is represented as a set of 3D Gaussian functions, each of which can be viewed as a fuzzy, directional ellipsoid describing the color and transparency of a region in space. The value of a 3D Gaussian function $G(x)$ at a point $x$ in space is defined by the following formula:

\begin{equation}
\label{gaussian}
G(\mathbf{x}) = α \cdot exp(-(1/2)  (\mathbf{x} - \mathbf{μ})^T  \mathbf{Σ}  (\mathbf{x} - \mathbf{μ}))
\end{equation}

Where $\mathbf{x}$ represents the position of a point in 3D space $(x, y, z)$. $\boldsymbol{\mu}$ represents the mean of the Gaussian function $(μx, μy, μz)$, that is, the position of the Gaussian in space. $\boldsymbol{\Sigma}$ is the covariance matrix of the Gaussian function, which describes the extension and shape of the Gaussian in all directions. $\alpha$ is the opacity of the Gaussian, which controls the contribution of the Gaussian to the final rendered color.
Since the covariance matrix has physical meaning only when it is a semi-positive matrix, directly using the stochastic gradient descent method to optimize the covariance matrix is likely to obtain invalid results. In order to better control the shape and direction of the Gaussian to optimize it, 3DGS\cite{kerbl20233dgaussiansplattingrealtime} decomposes the covariance matrix $Σ$ into a rotation matrix $R$ and a scaling matrix $S$:

\begin{equation}
\label{rotation}
\mathbf{Σ} = \mathbf{R} \mathbf{S} \mathbf{S}^T \mathbf{R}^T
\end{equation}


Where $S$ is the scaling matrix, and its diagonal elements $s = (sx, sy, sz)$ represent the scaling factors of the Gaussian in the three main axis directions. And $R$ is the rotation matrix, which represents the orientation of the Gaussian, represented by four quaternion parameters $q = (qw, qx, qy, qz)$.
In order to store the light distribution information of the radiation field, each Gaussian function is associated with a color information $c$ in addition to the position, shape and opacity, usually expressed as an RGB color vector. This color information is usually represented by spherical harmonics (SH) to support view-related effects.


\subsection{Rendering of gaussian splatting}
Given a camera view, rendering the image of that view requires projecting the 3D Gaussian onto the 2D image plane, and then mixing the Gaussian functions projected onto the 2D image plane to get the final rendering result.
View Transformation uses the view transformation matrix $W$ to transform the Gaussian center point $\mu$ from the world coordinate system to the camera coordinate system:

\begin{equation}
\label{view_transformation}
\boldsymbol{\mu'} = \mathbf{W} \boldsymbol{\mu}
\end{equation}

Similarly, the covariance matrix also needs to be transformed to the camera coordinate system:

\begin{equation}
\label{covariance_transformation}
\boldsymbol{\Sigma'} = \mathbf{J} \mathbf{W} \boldsymbol{\Sigma} \mathbf{W}^T \mathbf{J}^T
\end{equation}

Where J is the Jacobian matrix of the projection transformation.
The projected Gaussian is still a Gaussian function, but now it is on the 2D image plane. Its center point is the projected $\mu'$, and the covariance matrix is the first two rows and two columns of $\Sigma'$.
Next, the Gaussian function projected onto the 2D image plane needs to be alpha blended. For each pixel on the image plane, the color contributed by all 2D Gaussians covering the pixel needs to be calculated. 3DGS \cite{kerbl20233dgaussiansplattingrealtime} uses a tile-based rasterizer for efficient alpha blending. For each pixel, it sorts the Gaussians from front to back and calculates the final color using the following formula:

\begin{equation}
\label{alpha_blending}
C = \sum_i c_i \cdot \alpha_i \cdot \prod_{j<i} (1 - \alpha_j)
\end{equation}

Where: $C$ is the final pixel color. $c_i$ is the color of the i-th Gaussian. $\alpha_i$ is the 2D opacity of the projected i-th Gaussian, which can be calculated from the projected 2D Gaussian function. $∏_{j<i} (1 - α_{j})$ represents the cumulative multiplication of the opacities of all Gaussians before the i-th Gaussian, which is used to calculate the degree of occlusion of the current Gaussian function.

\subsection{Optimization of 3DGS}
3DGS uses a combination of L1 loss and structural similarity index (SSIM) as a loss function to measure the difference between the rendered image and the real image:

\begin{equation}
\label{loss_function}
\mathcal{L} = (1-\lambda)\mathcal{L}_1 + \lambda\mathcal{L}_{D-SSIM}
\end{equation}


Through the back-propagation algorithm, the gradient of the loss function with respect to each Gaussian parameter $(μ, q, s, α, c)$ can be calculated. Among them, $q$ and $s$ are the rotation and scaling of the Gaussian ball obtained by decomposing the covariance matrix of the Gaussian function. These two parameters are optimized to prevent invalid covariance matrices. Since the entire rendering process is differentiable, optimization algorithms such as stochastic gradient descent can be used to update these parameters to make the rendered image closer to the real image.

During the optimization process, 3DGS uses the method of adaptive density control to densify and simplify the Gaussian as needed. For example, for under-reconstructed areas (i.e. areas with large differences between the rendered image and the real image), Gaussians are copied or split to increase the representation of the area, while for over-reconstructed or areas with very low transparency, Gaussians are removed or merged to reduce redundancy and improve efficiency.

\subsection{Algorithm flow of 3DGS}

The algorithm flow of 3DGS can be summarized as follows:

\begin{enumerate}
\item Input: A set of images from different views of the scene.
\item Estimate the camera poses and a sparse point cloud from the input images using Structure from Motion (SfM).
\item Convert each point in the sparse point cloud to a 3D Gaussian function, initializing its parameters.
\item Render an image from one of the input views using splatting.
\item Compare the rendered image with the real image and update the Gaussian parameters using backpropagation.
\item Use adaptive density control to densify or simplify the Gaussian as needed.
\item Repeat steps 4-6 until the Gaussian parameters converge.
\item Render an image from the desired view using splatting.
\end{enumerate}

\section{Advantages and Disadvantages of 3DGS}

\subsection{Advantages of 3DGS}

\begin{itemize}
  \item Efficient rendering speed: Compared with NeRF's volume rendering method, which requires multiple sampling and neural network queries for each ray, 3DGS projects the Gaussian function into two-dimensional space by splashing, which can achieve real-time or near real-time rendering speed and is suitable for interactive applications.

  \item Compact data representation: Compared with dense polygonal meshes or neural networks with large data volumes, Gaussian function representation is more compact, which means less storage space and computing power are required.

  \item Editability: Since the Gaussian function of 3DGS is an explicit expression of the three-dimensional object field, the shape, position, color and other information of the Gaussian function can be easily edited to modify the scene, which is better than NeRF's implicit neural network expression in editability.

  \item Robustness: 3DGS is more robust to noise, produces fewer visual artifacts, and can handle traditionally challenging aspects of three-dimensional reconstruction, such as transparency and albedo, more reliably than past technologies.
\end{itemize}

\subsection{Disadvantages of 3DGS}

\begin{itemize}
  \item Too many Gaussian functions: Using millions of Gaussian functions to represent the scene results in high memory usage, for example, the memory usage of a typical scene is 700Mb-1.2Gb.

  \item Need to capture the scene from multiple angles: In order to obtain high-quality reconstruction results, scene images need to be taken from multiple angles.

  \item Cannot generate meshes directly: The original 3DGS implementation cannot directly generate mesh models.

  \item Sensitive to noise: In the case of few samples, 3DGS is more susceptible to noise than 3D reconstruction based on neural radiation fields.
\end{itemize}

\section{Improvement Methods of 3DGS}
In order to further improve the performance of 3DGS, researchers have proposed various improvement methods, mainly focusing on the following aspects:

\subsection{Speed and efficiency improvement methods}
Papantonakis et al.\cite{papantonakis2024reducing} observes that when 3DGS renders an image of a given resolution, dense clusters of Gaussian functions may appear projected to the same pixel, and the number of details contained in these Gaussian functions exceeds the range that can be distinguished from any view. Based on this, this article proposes a resolution-aware Gaussian function pruning strategy. This strategy identifies and removes unnecessary Gaussian functions by evaluating the redundancy score in space. At the same time, this article uses adaptive spherical harmonic function orders to evaluate the spherical harmonic function of each Gaussian to determine whether it needs a higher-order spherical harmonic function to represent the color. For many diffuse materials, only RGB color values are used, and there is no need to store additional spherical harmonic functions. Furthermore, this article quantizes the properties of the Gaussian function such as opacity, scale, rotation, and spherical harmonic function coefficients to compress its memory usage.

The 3DGS-LM\cite{hollein20243dgs} method uses the Levenberg-Marquardt (LM) optimizer instead of the ADAM optimizer to accelerate the reconstruction process of 3DGS. The 3DGS-LM method modifies the loss function of 3DGS rendering into a square sum energy function and uses the LM algorithm instead of the ADAM optimizer for optimization. To make the LM algorithm more computationally efficient, the article also uses an efficient GPU parallelization scheme for the preconditioned conjugate gradient (PCG) algorithm inside the LM algorithm, and customizes the CUDA kernel to calculate the Jacobian vector product.

FCGS\cite{chen2024fast} proposes a compression method that does not require optimization, which can quickly compress 3DGS scenes in a single feedforward pass to achieve the purpose of quickly compressing 3DGS. Since the Gaussian attributes of 3DGS are very sensitive to bias, directly feeding all attributes to the autoencoder will result in a significant decrease in fidelity. To solve this problem, FCGS introduces a multipath entropy module (MEM) to adaptively determine whether the Gaussian attributes should be compressed through the autoencoder or directly quantized. To further improve the accuracy of probability estimation, FCGS adopts a context model. The core idea is to use the decoded part of the data to predict the rest, and to eliminate the redundancy between Gaussians by exploiting the 3D and 2D grid structures to capture the spatial relationship between Gaussian attributes. By combining MEM and context models, FCGS achieves more than 20 times compression while maintaining high fidelity.

\subsection{Rendering quality and detail improvement methods}
Since the noisy and discrete nature of 3D Gaussians hinders accurate estimation of object surfaces, Normal-GS\cite{wei2024normal} incorporates surface normals into the 3DGS pipeline to improve rendering quality and geometric accuracy. Normal-GS uses physically based rendering equations to simulate the interaction between normals and incident light, reparameterizing surface color as the product of normals and designed integrated directional illumination vectors (IDIV). IDIV captures local incident light without relying on a global environment map, providing a more flexible and accurate framework to optimize surface normals. Normal-GS also leverages optimized normals and integrated directional encoding (IDE) to accurately model specular effects, thereby enhancing rendering quality and surface normal accuracy.

Liu et al.\cite{liu2024atomgs} observed that 3DGS's strategy of combining optimization parameters with adaptive density control may lead to the generation of noise and artifacts. AtomGS first uses isotropic atomic Gaussian spheres for distribution and optimizes the distribution of these Gaussian functions. Compared with anisotropic Gaussian functions, atomic Gaussians can be more accurately distributed on the surface of objects after rapid optimization. Next, the Gaussian sphere is subdivided and merged, merging the Gaussian function that represents a large and smooth surface while retaining the Gaussian function that represents the details. In order to solve the problem that the Gaussian function does not always represent the actual geometric structure, AtomGS also uses geometry-guided optimization. It calculates the curvature map and the edge map, and smoothes the curvature map by combining the edge map with a weight function. The design of this weight function makes the area with lower gradient get higher weights, thereby promoting more smoothing, while the area with higher gradient gets lower weights to retain details.

Mip-Splatting\cite{yu2024mip} is proposed to address the problem of artifacts in 3DGS when the sampling rate changes. This method mainly introduces two new filters: 3D smoothing filter and 2D Mip filter. The role of the 3D smoothing filter is to limit the maximum frequency of the 3D Gaussian primitives to match the sampling constraints of the training image. According to the Nyquist-Shannon sampling theorem, the highest frequency of the 3D scene is limited by the sampling rate defined by the training view. The role of 2D Mip filter is to simulate the box filter in the physical imaging process to achieve non-aliased rendering at any scale.

Lee et al.\cite{lee2024deblurring} proposed a new real-time deblurring framework called Deblurring 3DGS, which aims to improve 3DGS so that it can reconstruct fine and clear details from blurred images. It models two types of blur, defocus blur and camera motion blur in Gaussian function expression, and uses a multi-layer perceptron to generate a simulated blur 3DGS from a clear picture optimized 3DGS. It is then used as input to train a neural network to achieve the purpose of deblurring. Deblurring 3DGS can reconstruct fine and clear details even from blurred images.




\section{Application of 3DGS}


3DGS has broad application prospects in various fields due to its high-quality and real-time rendering characteristics, such as:
3D semantic segmentation: Since the amount of 3D data is larger than that of 2D data, object segmentation in 3D scenes has always been a very challenging task. Traditional 3D scene segmentation methods are usually based on back-projection of 2D projection slices, voxels or point clouds. Since the Gaussian sphere in 3DGS is similar to point cloud, the additional information carried by 3DGS can be used to improve the point cloud segmentation method. PointNet++ was originally developed for point cloud segmentation and can now be used as a 3DGS segmentation model\cite{jurski2024semantic}.
Creating realistic virtual characters: Meta\cite{saito2024relightablegaussiancodecavatars} demonstrated its ability to achieve realism and remote real-time rendering in VR environments through 3DGS experiments on virtual characters. It used editable 3DGS scenes to achieve expression changes of virtual characters and real-time lighting changes.
Simultaneous localization and mapping (SLAM): SplaTAM\cite{keetha2024splatam} used 3DGS to perform accurate camera tracking and high-fidelity reconstruction in real scenes, demonstrating its practicality in complex spatial mapping and 3D reconstruction.
Game development: used to create realistic game scenes and character models, and to achieve real-time game rendering. For example, the game engine Unreal Engine\cite{unrealengine} can integrate 3DGS technology into the engine through the use of plug-ins, providing game developers with more powerful tools.
Medical imaging: used to reconstruct three-dimensional models of medical data, and perform disease diagnosis and treatment planning. For example, 3DGS can be used to create three-dimensional models of human organs to help doctors plan surgeries\cite{cai2024radiative}.
Autonomous driving: It is very difficult for traditional methods to quickly reconstruct the three-dimensional scene of the road from the on-board sensor data with a small number of viewing angles, especially under high-speed motion, the image used for reconstruction is likely to be blurred. DrivingGaussian\cite{zhou2024drivinggaussian} uses the capabilities of 3DGS to reconstruct more accurate scenes under limited viewing angles.


\section{Conclusion}
As an emerging 3D reconstruction technology, 3DGS uses 3D object field expression and differentiable rendering, and has the advantages of high quality, real-time rendering, and compact data representation. It has broad application prospects in the fields of 3D semantic segmentation, real-time positioning and map reconstruction, and game development. Compared with NeRF, 3DGS has advantages in rendering speed, robustness to noise, and editability, but it takes up a lot of storage space. With the deepening of research and the continuous development of technology, 3DGS will play a greater role in the future and promote the development of 3D reconstruction technology.


\bibliographystyle{IEEEtran}
\bibliography{references}





\end{document}



